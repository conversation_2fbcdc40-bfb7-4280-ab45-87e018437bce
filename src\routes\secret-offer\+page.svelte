<script lang="ts">
    import { <PERSON><PERSON>, PopUp, H1, H2, H3, P1 } from '$lib/ui';
	import { innerWidth } from 'svelte/reactivity/window';

    let isPopup = $state(false);

    let now = $state(Date.now());
    let end = new Date("Oct 5, 2025 00:00:00 GMT+07:00").getTime();
    const start = new Date("Sep 25, 2024 00:00:00 GMT+07:00").getTime();

    let interval = $state<NodeJS.Timeout | null>(null);

    // Start the interval when component mounts
    $effect(() => {
        interval = setInterval(() => {
            now = Date.now();
        }, 1000);

        // Cleanup interval when component unmounts
        return () => {
            if (interval) clearInterval(interval);
        };
    });

    // Derived countdown values
    let count = $derived(Math.round((end - now)));
    let d = $derived(Math.floor(count / (1000 * 60 * 60 * 24)));
    let h = $derived(Math.floor((count % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)));
    let m = $derived(Math.floor((count % (1000 * 60 * 60)) / (1000 * 60)));
    let s = $derived(Math.floor((count % (1000 * 60)) / 1000));

    // Clear interval when countdown reaches zero
    $effect(() => {
        if (count <= 0 && interval) {
            clearInterval(interval);
            interval = null;
        }
    });

    function handleClick() {
        isPopup = !isPopup;
    }
</script>

<svelte:head>
	<title>DSAT16 Secret Offer</title>
</svelte:head>

<div class='webpage'>
    <div class="course-info">
        <div class="title-container">
            <H1>DSAT16</H1>
            <P1>Chúc mừng! Bạn đã tìm thấy offer bí mật!</P1>
        </div>

        {@render priceSection()}
    </div>
</div>

{#snippet priceSection()}
	<div class="price-section">
		<div class="square">
			<svg xmlns="http://www.w3.org/2000/svg" width="12.86%" height="14.86%" viewBox="0 0 45 52" fill="none">
				<path d="M19.1707 51.0836H0.353271V0.174316H19.1456C24.3326 0.174316 28.7992 1.19398 32.5442 3.23214C36.3061 5.25398 39.2065 8.17064 41.2446 11.9821C43.2828 15.7773 44.3019 20.318 44.3019 25.6042C44.3019 30.9072 43.2828 35.4648 41.2446 39.2763C39.2228 43.0878 36.3312 46.0126 32.5693 48.0508C28.8074 50.0726 24.3414 51.0836 19.1707 51.0836ZM12.6579 40.5935H18.6982C21.549 40.5935 23.9599 40.1128 25.9321 39.1521C27.9207 38.1738 29.4205 36.5912 30.4314 34.4037C31.4586 32.1999 31.9726 29.2669 31.9726 25.6042C31.9726 21.942 31.4586 19.0253 30.4314 16.8542C29.4041 14.6667 27.8875 13.0922 25.8826 12.1315C23.894 11.1532 21.4411 10.6644 18.5244 10.6644H12.6579V40.5935Z" fill="black"/>
			</svg>
			<svg xmlns="http://www.w3.org/2000/svg" width="12.86%" height="15.14%" viewBox="0 0 42 53" fill="none">
				<path d="M29.0039 16.4369C28.8382 14.6139 28.1009 13.197 26.7913 12.1861C25.4986 11.1589 23.6512 10.645 21.2484 10.645C19.6571 10.645 18.3318 10.852 17.2707 11.2662C16.2102 11.681 15.4151 12.2526 14.8843 12.9818C14.354 13.694 14.081 14.5148 14.0641 15.4429C14.0309 16.2047 14.1802 16.8761 14.5115 17.4559C14.8598 18.0364 15.3568 18.5497 16.0031 18.9971C16.6658 19.4282 17.4614 19.8091 18.3895 20.141C19.3176 20.4724 20.3618 20.7623 21.5214 21.0108L25.8964 22.0054C28.4153 22.5519 30.636 23.2811 32.5587 24.1929C34.4977 25.104 36.1217 26.1896 37.4307 27.449C38.7566 28.7084 39.7594 30.1586 40.4384 31.7989C41.1179 33.4399 41.4662 35.2791 41.4825 37.3179C41.4662 40.5326 40.6542 43.2918 39.0465 45.5954C37.4389 47.8989 35.1271 49.6635 32.1113 50.8903C29.1118 52.1164 25.4904 52.7295 21.2484 52.7295C16.9889 52.7295 13.2772 52.0914 10.112 50.8156C6.94686 49.5393 4.48577 47.6003 2.72877 44.9986C0.972357 42.3969 0.0693587 39.1075 0.0197754 35.1304H11.8019C11.9017 36.7707 12.3409 38.138 13.1197 39.2318C13.8984 40.3255 14.9677 41.1539 16.3263 41.7174C17.7018 42.2809 19.2925 42.5626 21.0991 42.5626C22.7564 42.5626 24.1651 42.3392 25.3248 41.8918C26.5014 41.4438 27.4049 40.8225 28.0344 40.0269C28.6644 39.2318 28.9875 38.32 29.0039 37.2928C28.9875 36.3314 28.6889 35.5113 28.109 34.8317C27.5292 34.1358 26.6344 33.539 25.4245 33.042C24.231 32.5281 22.7068 32.0562 20.8506 31.6251L15.5306 30.382C11.1229 29.3711 7.65094 27.7389 5.11519 25.4855C2.58003 23.2146 1.32061 20.1492 1.33694 16.2875C1.32061 13.1393 2.16527 10.3801 3.87269 8.01004C5.57952 5.64054 7.94086 3.79253 10.9573 2.46661C13.9731 1.14128 17.4119 0.478027 21.2729 0.478027C25.2174 0.478027 28.6393 1.14944 31.5396 2.49169C34.4563 3.81761 36.7179 5.68195 38.3255 8.0847C39.9332 10.4874 40.7534 13.2717 40.7866 16.4369H29.0039Z" fill="black"/>
			</svg>
			<svg xmlns="http://www.w3.org/2000/svg" width="14.86%" height="14.86%" viewBox="0 0 52 52" fill="none">
				<path d="M14.1368 51.0836H0.912598L18.0894 0.174316H34.4706L51.6474 51.0836H38.4233L26.4661 13.0012H26.0689L14.1368 51.0836ZM12.3716 31.0484H40.014V40.3946H12.3716V31.0484Z" fill="black"/>
			</svg>
			<svg xmlns="http://www.w3.org/2000/svg" width="12.57%" height="14.86%" viewBox="0 0 44 52" fill="none">
				<path d="M0.105957 10.1674V0.174316H43.135V10.1674H27.6982V51.0836H15.5672V10.1674H0.105957Z" fill="black"/>
			</svg>
			<svg xmlns="http://www.w3.org/2000/svg" width="11.43%" height="15.14%" viewBox="0 0 25 52" fill="none">
				<path d="M24.5848 0.174316V51.0836H12.3051V11.659H12.007L0.62207 18.6193V7.9799L13.1754 0.174316H24.5848Z" fill="black"/>
			</svg>
			<svg xmlns="http://www.w3.org/2000/svg" width="12.86%" height="15.14%" viewBox="0 0 41 53" fill="none">
				<path d="M21.2485 52.7791C18.4642 52.7791 15.796 52.3317 13.244 51.4369C10.6919 50.5257 8.42154 49.0837 6.43295 47.1114C4.44437 45.1229 2.87811 42.5212 1.73478 39.3064C0.591445 36.0748 0.0279571 32.139 0.0442904 27.4986C0.0612071 23.3062 0.574539 19.5443 1.58546 16.2134C2.59637 12.8657 4.03836 10.0237 5.91086 7.68686C7.80028 5.35003 10.0537 3.56854 12.6723 2.34237C15.3072 1.09987 18.249 0.478027 21.497 0.478027C25.06 0.478027 28.2 1.17395 30.9178 2.56637C33.6525 3.94187 35.84 5.79744 37.4803 8.13428C39.1212 10.4542 40.0907 13.0395 40.3888 15.8903H28.2829C27.9183 14.2826 27.1145 13.0646 25.872 12.2357C24.6452 11.391 23.1869 10.9681 21.497 10.9681C18.3814 10.9681 16.0527 12.3185 14.5115 15.02C12.9873 17.7214 12.208 21.3339 12.1753 25.8583H12.4985C13.1944 24.3334 14.1971 23.0244 15.5061 21.9307C16.8151 20.8369 18.3149 19.9999 20.0054 19.42C21.7122 18.8233 23.5188 18.5252 25.4245 18.5252C28.4736 18.5252 31.1663 19.2293 33.5031 20.638C35.84 22.0468 37.671 23.977 38.9969 26.43C40.3223 28.866 40.9774 31.6584 40.9605 34.8072C40.9774 38.3533 40.1485 41.4852 38.4749 44.2035C36.8013 46.9044 34.4808 49.009 31.5145 50.517C28.5646 52.0255 25.1428 52.7791 21.2485 52.7791ZM21.1738 43.3332C22.6817 43.3332 24.0321 42.9768 25.2256 42.2645C26.4185 41.5517 27.3548 40.5822 28.0344 39.356C28.714 38.1299 29.0453 36.7456 29.0289 35.2044C29.0453 33.6469 28.714 32.2633 28.0344 31.0535C27.3717 29.8436 26.4436 28.8823 25.2501 28.17C24.0735 27.4572 22.7231 27.1008 21.1983 27.1008C20.0882 27.1008 19.0522 27.3079 18.0914 27.7226C17.1301 28.1368 16.293 28.7166 15.5808 29.4627C14.8843 30.1919 14.3377 31.0534 13.9399 32.048C13.542 33.0257 13.3349 34.0862 13.3186 35.2295C13.3349 36.7374 13.6832 38.1048 14.3628 39.331C15.0418 40.5577 15.9699 41.5354 17.1465 42.2645C18.323 42.9768 19.6653 43.3332 21.1738 43.3332Z" fill="black"/>
			</svg>
		</div>

		<div class="price-part">
			<H2><span class="original-price">₫</span>5.160.000</H2>
			<Button onclick={handleClick}>Tham gia</Button>
		</div>
	</div>
{/snippet}

<style>
    .webpage {
        width: 100%;
        font-size: 1rem;
        -webkit-tap-highlight-color: transparent;
    }

    .course-info {
        max-width: 90rem;
        margin: 4rem;
		display: flex;
		gap: 3rem;
		justify-content: space-between;
    }

    .price-section {
        display: flex;
        flex-direction: column;
        gap: 0.625rem;
        margin-left: 3.125rem;
        margin-bottom: 3.125rem;
        align-items: center;
    }

    .square {
        width: 21.875rem;
        height: 21.875rem;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--sky-blue);
        border-radius: 1.25rem;
        border: black solid 0.125rem;
        box-shadow: 0.25rem 0.25rem 0 0;
    }

    .price-part {
        display: flex;
        gap: 1.25rem;
        align-items: center;
    }

    .title-container {
        max-width: 47.4375rem;
    }

	@media (min-width: 1570px) {
        .course-info {
            margin: 4rem auto;
        }
    }

    @media (max-width: 1240px) {
        .square {
            width: 17.1875rem;
            height: 17.1875rem;
        }

        .original-price {
            font-size: 1.125rem;
        }

        .price-part {
            gap: 0.625rem;
        }
    }

    @media (max-width: 768px) {
        .price-section {
            margin: 3.75rem 0;
        }
    }

</style>